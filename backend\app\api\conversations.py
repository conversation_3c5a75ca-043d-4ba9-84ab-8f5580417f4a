from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import json
from datetime import datetime

from ..core.database import get_db
from ..models.conversation import Conversation, Message, MessageType, MessageContentType
from ..models.app import App
from ..models.user import User
from .auth import get_current_user
from ..services.rag_engine import rag_engine

router = APIRouter()


class ConversationCreate(BaseModel):
    title: str = None
    app_id: int
    model_name: str = "gpt-3.5-turbo"
    system_prompt: str = None
    temperature: str = "0.7"
    max_tokens: int = 2000


class MessageCreate(BaseModel):
    content: str
    message_type: MessageType = MessageType.USER
    content_type: MessageContentType = MessageContentType.TEXT


class ChatRequest(BaseModel):
    message: str
    stream: bool = False
    temperature: float = 0.7
    max_tokens: int = 2000


class GeneralChatRequest(BaseModel):
    message: str
    app_id: int
    conversation_id: Optional[int] = None
    stream: bool = False
    temperature: float = 0.7
    max_tokens: int = 2000


class ChatResponse(BaseModel):
    response: str
    conversation_id: int
    app_id: int
    model_name: str = None
    rag_enabled: bool = False
    retrieved_chunks: Optional[List[dict]] = None
    knowledge_bases_used: Optional[List[str]] = None
    response_time: Optional[str] = None
    timestamp: str


class MessageResponse(BaseModel):
    id: int
    content: str
    message_type: MessageType
    content_type: MessageContentType
    model_name: str = None
    retrieved_chunks: Optional[List[dict]] = None
    similarity_scores: Optional[List[float]] = None
    response_time: Optional[str] = None
    created_at: str

    class Config:
        from_attributes = True


class ConversationResponse(BaseModel):
    id: int
    title: str = None
    app_id: int
    model_name: str
    is_active: bool
    message_count: int
    created_at: str
    last_message_at: str = None
    
    class Config:
        from_attributes = True


@router.post("/", response_model=ConversationResponse)
async def create_conversation(
    conv_data: ConversationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新对话"""
    # 检查应用是否存在且用户有权限
    app = db.query(App).filter(App.id == conv_data.app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    if app.owner_id != current_user.id and not app.is_public:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    db_conversation = Conversation(
        title=conv_data.title or f"对话 - {app.name}",
        app_id=conv_data.app_id,
        user_id=current_user.id,
        model_name=conv_data.model_name,
        system_prompt=conv_data.system_prompt,
        temperature=conv_data.temperature,
        max_tokens=conv_data.max_tokens
    )
    
    db.add(db_conversation)
    db.commit()
    db.refresh(db_conversation)
    
    return db_conversation


@router.get("/", response_model=List[ConversationResponse])
async def get_conversations(
    app_id: int = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的对话列表"""
    query = db.query(Conversation).filter(Conversation.user_id == current_user.id)
    
    if app_id:
        query = query.filter(Conversation.app_id == app_id)
    
    conversations = query.offset(skip).limit(limit).all()
    return conversations


@router.get("/{conv_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conv_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取对话消息"""
    conversation = db.query(Conversation).filter(Conversation.id == conv_id).first()
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="对话不存在"
        )
    
    if conversation.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    messages = db.query(Message).filter(
        Message.conversation_id == conv_id
    ).offset(skip).limit(limit).all()
    
    return messages


@router.post("/{conv_id}/chat", response_model=MessageResponse)
async def chat_with_conversation(
    conv_id: int,
    chat_request: ChatRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """与对话进行聊天（集成RAG）"""
    conversation = db.query(Conversation).filter(Conversation.id == conv_id).first()
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="对话不存在"
        )

    if conversation.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    try:
        # 创建用户消息
        user_message = Message(
            content=chat_request.message,
            message_type=MessageType.USER,
            content_type=MessageContentType.TEXT,
            conversation_id=conv_id
        )
        db.add(user_message)
        db.commit()
        db.refresh(user_message)

        # 获取对话历史
        conversation_history = rag_engine.get_conversation_context(conv_id, max_messages=10)

        # 使用RAG引擎生成回复
        response = await rag_engine.chat_with_knowledge(
            query=chat_request.message,
            app_id=conversation.app_id,
            conversation_history=conversation_history,
            model=conversation.model_name,
            temperature=float(conversation.temperature),
            max_tokens=conversation.max_tokens,
            stream=chat_request.stream
        )

        if isinstance(response, dict) and 'content' in response:
            # 创建AI回复消息
            ai_message = Message(
                content=response['content'],
                message_type=MessageType.ASSISTANT,
                content_type=MessageContentType.TEXT,
                conversation_id=conv_id,
                model_name=response.get('model'),
                retrieved_chunks=json.dumps(response.get('retrieved_chunks', [])),
                similarity_scores=json.dumps([
                    chunk.get('similarity_score', 0)
                    for chunk in response.get('retrieved_chunks', [])
                ]),
                prompt_tokens=response.get('usage', {}).get('prompt_tokens'),
                completion_tokens=response.get('usage', {}).get('completion_tokens'),
                total_tokens=response.get('usage', {}).get('total_tokens'),
                response_time=str(response.get('response_time', 0))
            )

            db.add(ai_message)

            # 更新对话信息
            conversation.message_count += 2  # 用户消息 + AI回复
            conversation.last_message_at = datetime.utcnow()

            # 如果是第一条消息，生成对话标题
            if conversation.message_count == 2 and not conversation.title:
                title = await rag_engine.generate_title(chat_request.message)
                conversation.title = title

            db.commit()
            db.refresh(ai_message)

            return ai_message
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AI回复生成失败"
            )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天失败: {str(e)}"
        )


@router.post("/{conv_id}/messages", response_model=MessageResponse)
async def send_message(
    conv_id: int,
    message_data: MessageCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """发送消息（简单版本，不使用RAG）"""
    conversation = db.query(Conversation).filter(Conversation.id == conv_id).first()
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="对话不存在"
        )

    if conversation.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 创建用户消息
    user_message = Message(
        content=message_data.content,
        message_type=message_data.message_type,
        content_type=message_data.content_type,
        conversation_id=conv_id
    )

    db.add(user_message)
    db.commit()
    db.refresh(user_message)

    return user_message


@router.post("/chat", response_model=ChatResponse)
async def general_chat(
    chat_request: GeneralChatRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """通用聊天接口（自动创建或使用现有对话）"""

    # 检查应用是否存在且用户有权限
    app = db.query(App).filter(App.id == chat_request.app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )

    if app.owner_id != current_user.id and not app.is_public:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 获取或创建对话
    conversation = None
    if chat_request.conversation_id:
        conversation = db.query(Conversation).filter(
            Conversation.id == chat_request.conversation_id,
            Conversation.user_id == current_user.id
        ).first()

    if not conversation:
        # 创建新对话
        model_config = {}
        if app.model_config:
            try:
                model_config = json.loads(app.model_config)
            except:
                model_config = {}

        conversation = Conversation(
            title=f"与{app.name}的对话",
            app_id=chat_request.app_id,
            user_id=current_user.id,
            model_name=model_config.get('model', 'gpt-3.5-turbo'),
            system_prompt=app.system_prompt,
            temperature=str(chat_request.temperature),
            max_tokens=chat_request.max_tokens
        )
        db.add(conversation)
        db.commit()
        db.refresh(conversation)

    try:
        # 创建用户消息
        user_message = Message(
            content=chat_request.message,
            message_type=MessageType.USER,
            content_type=MessageContentType.TEXT,
            conversation_id=conversation.id
        )
        db.add(user_message)
        db.commit()
        db.refresh(user_message)

        # 获取对话历史
        conversation_history = rag_engine.get_conversation_context(conversation.id, max_messages=10, db=db)
        print(f"聊天API: 准备调用RAG引擎 - 应用ID: {conversation.app_id}, 查询: '{chat_request.message}'")

        # 使用RAG引擎生成回复
        response = await rag_engine.chat_with_knowledge(
            query=chat_request.message,
            app_id=conversation.app_id,
            conversation_history=conversation_history,
            model=conversation.model_name,
            temperature=float(conversation.temperature),
            max_tokens=conversation.max_tokens,
            stream=chat_request.stream,
            db=db
        )

        print(f"聊天API: RAG引擎返回响应 - RAG启用: {response.get('rag_enabled', False)}")

        if isinstance(response, dict) and 'content' in response:
            # 创建AI回复消息
            ai_message = Message(
                content=response['content'],
                message_type=MessageType.ASSISTANT,
                content_type=MessageContentType.TEXT,
                conversation_id=conversation.id,
                model_name=response.get('model'),
                retrieved_chunks=json.dumps(response.get('retrieved_chunks', [])),
                similarity_scores=json.dumps([
                    chunk.get('similarity_score', 0)
                    for chunk in response.get('retrieved_chunks', [])
                ]),
                prompt_tokens=response.get('usage', {}).get('prompt_tokens'),
                completion_tokens=response.get('usage', {}).get('completion_tokens'),
                total_tokens=response.get('usage', {}).get('total_tokens'),
                response_time=str(response.get('response_time', 0))
            )

            db.add(ai_message)
            db.commit()
            db.refresh(ai_message)

            # 更新对话的最后消息时间
            conversation.last_message_at = datetime.now()
            db.commit()

            return ChatResponse(
                response=response['content'],
                conversation_id=conversation.id,
                app_id=conversation.app_id,
                model_name=response.get('model'),
                rag_enabled=response.get('rag_enabled', False),
                retrieved_chunks=response.get('retrieved_chunks', []),
                knowledge_bases_used=response.get('knowledge_bases_used', []),
                response_time=str(response.get('response_time', 0)),
                timestamp=datetime.now().isoformat()
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AI回复生成失败"
            )

    except Exception as e:
        try:
            db.rollback()
        except Exception:
            # 如果回滚失败，忽略错误（连接可能已关闭）
            pass
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天处理失败: {str(e)}"
        )
