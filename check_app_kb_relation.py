#!/usr/bin/env python3
"""
检查应用和知识库的关联关系
"""

import sys
import os

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import get_db
from app.models.app import App
from app.models.knowledge_base import KnowledgeBase

def check_app_kb_relation():
    """检查应用和知识库的关联关系"""
    print("🔍 检查应用和知识库的关联关系")
    
    db = next(get_db())
    try:
        # 检查应用5的知识库
        app = db.query(App).filter(App.id == 5).first()
        if app:
            print(f'\n📱 应用: {app.name} (ID: {app.id})')
            print(f'   所有者: {app.owner_id}')
            
            # 查找关联的知识库
            kbs = db.query(KnowledgeBase).filter(KnowledgeBase.app_id == 5).all()
            print(f'   关联的知识库数量: {len(kbs)}')
            for kb in kbs:
                print(f'     - {kb.name} (ID: {kb.id}, 活跃: {kb.is_active})')
        else:
            print('\n❌ 应用5不存在')
            
        # 检查知识库8
        kb8 = db.query(KnowledgeBase).filter(KnowledgeBase.id == 8).first()
        if kb8:
            print(f'\n📚 知识库8: {kb8.name}')
            print(f'   应用ID: {kb8.app_id}')
            print(f'   活跃状态: {kb8.is_active}')
            print(f'   描述: {kb8.description}')
        else:
            print('\n❌ 知识库8不存在')
            
        # 列出所有应用和知识库
        print(f'\n📋 所有应用:')
        all_apps = db.query(App).all()
        for app in all_apps:
            print(f'   - {app.name} (ID: {app.id}, 所有者: {app.owner_id})')
            
        print(f'\n📋 所有知识库:')
        all_kbs = db.query(KnowledgeBase).all()
        for kb in all_kbs:
            print(f'   - {kb.name} (ID: {kb.id}, 应用ID: {kb.app_id}, 活跃: {kb.is_active})')
            
    finally:
        db.close()

if __name__ == "__main__":
    check_app_kb_relation()
