#!/usr/bin/env python3
"""
测试RAG聊天功能
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_rag_chat():
    """测试RAG聊天功能"""
    print("🔍 测试RAG聊天功能")
    
    try:
        # 1. 登录获取token
        print("\n1. 登录...")
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
            'username': 'admin',
            'password': 'admin123'
        })
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return
        
        token = login_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print("✅ 登录成功")
        
        # 2. 测试RAG聊天
        print("\n2. 测试RAG聊天...")
        chat_data = {
            'message': '什么是机器学习？请详细解释。',
            'app_id': 5,  # 使用整数
            'temperature': 0.7,
            'max_tokens': 2000
        }
        
        print(f"   发送请求: {json.dumps(chat_data, ensure_ascii=False)}")
        
        chat_response = requests.post(f"{BASE_URL}/api/conversations/chat", 
                                     json=chat_data, headers=headers)
        
        print(f"   响应状态码: {chat_response.status_code}")
        
        if chat_response.status_code == 200:
            result = chat_response.json()
            print(f"   ✅ 聊天成功")
            print(f"   RAG启用: {result.get('rag_enabled', False)}")
            print(f"   知识库使用: {result.get('knowledge_bases_used', [])}")
            print(f"   检索片段数: {len(result.get('retrieved_chunks', []))}")
            print(f"   对话ID: {result.get('conversation_id')}")
            print(f"   应用ID: {result.get('app_id')}")
            print(f"   模型: {result.get('model_name')}")
            
            if result.get('retrieved_chunks'):
                print(f"\n   📚 检索到的知识片段:")
                for i, chunk in enumerate(result.get('retrieved_chunks', [])[:2], 1):
                    print(f"      片段 {i}:")
                    print(f"        相似度: {chunk.get('similarity_score', 0):.3f}")
                    print(f"        内容: {chunk.get('text', '')[:100]}...")
            
            print(f"\n   🤖 AI回答:")
            print(f"   {result.get('response', '无回答')[:300]}...")
            
            # 如果RAG没有启用，检查原因
            if not result.get('rag_enabled', False):
                print(f"\n   ⚠️ RAG未启用，可能的原因:")
                print(f"     - 知识库为空或不活跃")
                print(f"     - 相似度阈值过高")
                print(f"     - 向量搜索失败")
            
        else:
            print(f"   ❌ 聊天失败: {chat_response.status_code}")
            print(f"   错误信息: {chat_response.text}")
        
        # 3. 测试不同的查询
        print(f"\n3. 测试不同查询:")
        test_queries = [
            "人工智能有哪些应用？",
            "深度学习和机器学习的区别是什么？",
            "神经网络是如何工作的？"
        ]
        
        for query in test_queries:
            chat_data['message'] = query
            response = requests.post(f"{BASE_URL}/api/conversations/chat", 
                                   json=chat_data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                rag_enabled = result.get('rag_enabled', False)
                chunk_count = len(result.get('retrieved_chunks', []))
                print(f"   '{query[:30]}...': RAG={rag_enabled}, 片段数={chunk_count}")
            else:
                print(f"   '{query[:30]}...': 失败 ({response.status_code})")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_rag_chat()
