#!/usr/bin/env python3
"""
测试RAG搜索功能
"""

import sys
import os

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.vector_store import vector_store_manager

def test_rag_search():
    """测试RAG搜索功能"""
    print("🔍 测试RAG搜索功能")
    
    try:
        # 测试知识库8的搜索
        knowledge_base_id = 8
        query = "什么是机器学习？"
        top_k = 5
        
        print(f"\n📚 搜索知识库 {knowledge_base_id}")
        print(f"🔍 查询: {query}")
        print(f"📊 返回数量: {top_k}")
        
        # 执行搜索
        results = vector_store_manager.search_knowledge_base(
            knowledge_base_id, query, top_k
        )
        
        print(f"\n📄 搜索结果:")
        print(f"   找到 {len(results)} 个结果")
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"\n   结果 {i}:")
                print(f"     相似度: {result.get('similarity_score', 0):.3f}")
                print(f"     内容: {result.get('text', '')[:100]}...")
                print(f"     元数据: {result.get('metadata', {})}")
        else:
            print("   ❌ 没有找到任何结果")
            
        # 测试不同的查询
        test_queries = [
            "人工智能",
            "深度学习",
            "神经网络",
            "机器学习算法",
            "AI技术"
        ]
        
        print(f"\n🧪 测试多个查询:")
        for query in test_queries:
            results = vector_store_manager.search_knowledge_base(
                knowledge_base_id, query, 3
            )
            print(f"   '{query}': {len(results)} 个结果")
            if results:
                best_score = max(result.get('similarity_score', 0) for result in results)
                print(f"     最高相似度: {best_score:.3f}")
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_rag_search()
