#!/usr/bin/env python3
"""
测试RAG引擎功能
"""

import sys
import os
import asyncio

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.rag_engine import rag_engine
from app.core.database import get_db

async def test_rag_engine():
    """测试RAG引擎功能"""
    print("🔍 测试RAG引擎功能")
    
    try:
        # 测试参数
        app_id = 5  # 测试应用
        query = "什么是机器学习？请详细解释。"
        
        print(f"\n📱 应用ID: {app_id}")
        print(f"🔍 查询: {query}")
        
        # 获取数据库会话
        db = next(get_db())
        
        # 调用RAG引擎
        response = await rag_engine.chat_with_knowledge(
            query=query,
            app_id=app_id,
            conversation_history=[],
            model="gpt-3.5-turbo",
            temperature=0.7,
            max_tokens=2000,
            top_k=5,
            similarity_threshold=0.7,
            stream=False,
            db=db
        )
        
        print(f"\n📄 RAG引擎响应:")
        print(f"   RAG启用: {response.get('rag_enabled', False)}")
        print(f"   知识库使用: {response.get('knowledge_bases_used', [])}")
        print(f"   检索片段数: {len(response.get('retrieved_chunks', []))}")
        
        if response.get('retrieved_chunks'):
            print(f"\n📚 检索到的知识片段:")
            for i, chunk in enumerate(response.get('retrieved_chunks', [])[:3], 1):
                print(f"     片段 {i}:")
                print(f"       相似度: {chunk.get('similarity_score', 0):.3f}")
                print(f"       内容: {chunk.get('text', '')[:100]}...")
        
        print(f"\n🤖 AI回答:")
        print(f"   {response.get('content', '无回答')[:200]}...")
        
        # 测试不同的相似度阈值
        print(f"\n🧪 测试不同相似度阈值:")
        thresholds = [0.5, 0.6, 0.7, 0.8, 0.9]
        
        for threshold in thresholds:
            response = await rag_engine.chat_with_knowledge(
                query=query,
                app_id=app_id,
                conversation_history=[],
                model="gpt-3.5-turbo",
                temperature=0.7,
                max_tokens=2000,
                top_k=5,
                similarity_threshold=threshold,
                stream=False,
                db=db
            )
            
            rag_enabled = response.get('rag_enabled', False)
            chunk_count = len(response.get('retrieved_chunks', []))
            print(f"   阈值 {threshold}: RAG启用={rag_enabled}, 片段数={chunk_count}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_rag_engine())
