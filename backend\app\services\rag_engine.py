import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from .llm_service import llm_manager
from .vector_store import vector_store_manager
from ..core.database import get_db
from ..models.knowledge_base import KnowledgeBase
from ..models.app import App
from sqlalchemy.orm import Session


class RAGEngine:
    """检索增强生成引擎"""
    
    def __init__(self):
        self.llm_manager = llm_manager
        self.vector_manager = vector_store_manager
    
    async def chat_with_knowledge(
        self,
        query: str,
        app_id: int,
        conversation_history: List[Dict[str, str]] = None,
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        stream: bool = False,
        db: Session = None
    ) -> Dict[str, Any]:
        """基于知识库的对话"""

        print(f"RAG引擎: 开始处理查询 - 应用ID: {app_id}, 查询: '{query}'")

        # 如果没有传入db会话，则创建新的
        if db is None:
            db = next(get_db())
            should_close = True
        else:
            should_close = False
        try:
            app = db.query(App).filter(App.id == app_id).first()
            if not app:
                print(f"RAG引擎: 应用不存在 - ID: {app_id}")
                raise ValueError(f"应用不存在: {app_id}")

            print(f"RAG引擎: 找到应用 - {app.name} (ID: {app.id})")
            
            # 获取应用的知识库
            knowledge_bases = db.query(KnowledgeBase).filter(
                KnowledgeBase.app_id == app_id,
                KnowledgeBase.is_active == True
            ).all()

            print(f"RAG引擎: 应用 {app_id} 找到 {len(knowledge_bases)} 个活跃知识库")
            for kb in knowledge_bases:
                print(f"  - 知识库: {kb.name} (ID: {kb.id})")

            if not knowledge_bases:
                # 没有知识库，直接使用LLM
                print(f"RAG引擎: 没有找到活跃的知识库，使用纯LLM模式")
                return await self._chat_without_knowledge(
                    query, app, conversation_history, model, temperature, max_tokens, stream
                )
            
            # 从所有知识库中检索相关内容
            retrieved_chunks = []
            for kb in knowledge_bases:
                print(f"RAG引擎: 在知识库 {kb.name} (ID: {kb.id}) 中搜索: '{query}'")
                chunks = self.vector_manager.search_knowledge_base(
                    kb.id, query, top_k
                )
                print(f"RAG引擎: 知识库 {kb.id} 返回 {len(chunks)} 个结果")

                # 过滤低相似度的结果
                filtered_chunks = [
                    chunk for chunk in chunks
                    if chunk.get('similarity_score', 0) >= similarity_threshold
                ]
                print(f"RAG引擎: 过滤后剩余 {len(filtered_chunks)} 个结果 (阈值: {similarity_threshold})")

                if filtered_chunks:
                    for i, chunk in enumerate(filtered_chunks[:2]):
                        print(f"  片段 {i+1}: 相似度 {chunk.get('similarity_score', 0):.3f}")

                retrieved_chunks.extend(filtered_chunks)
            
            # 按相似度排序并取前top_k个
            retrieved_chunks.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            retrieved_chunks = retrieved_chunks[:top_k]
            
            if not retrieved_chunks:
                # 没有找到相关内容，直接使用LLM
                return await self._chat_without_knowledge(
                    query, app, conversation_history, model, temperature, max_tokens, stream
                )
            
            # 构建RAG提示
            messages = self._build_rag_messages(
                query, retrieved_chunks, app, conversation_history
            )
            
            # 调用LLM生成回答
            response = await self.llm_manager.chat_completion(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            
            # 添加检索信息到响应
            if isinstance(response, dict):
                response['retrieved_chunks'] = retrieved_chunks
                response['knowledge_bases_used'] = [kb.name for kb in knowledge_bases]
                response['rag_enabled'] = True
            
            return response

        finally:
            if should_close:
                db.close()
    
    async def _chat_without_knowledge(
        self,
        query: str,
        app: App,
        conversation_history: List[Dict[str, str]] = None,
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        stream: bool = False
    ) -> Dict[str, Any]:
        """不使用知识库的对话"""
        
        messages = []
        
        # 添加系统提示
        system_prompt = app.system_prompt or "你是一个有用的AI助手。"
        messages.append({"role": "system", "content": system_prompt})
        
        # 添加对话历史
        if conversation_history:
            messages.extend(conversation_history[-10:])  # 只保留最近10轮对话
        
        # 添加用户查询
        messages.append({"role": "user", "content": query})
        
        # 调用LLM
        response = await self.llm_manager.chat_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=stream
        )
        
        if isinstance(response, dict):
            response['rag_enabled'] = False
            response['retrieved_chunks'] = []
            response['knowledge_bases_used'] = []
        
        return response
    
    def _build_rag_messages(
        self,
        query: str,
        retrieved_chunks: List[Dict],
        app: App,
        conversation_history: List[Dict[str, str]] = None
    ) -> List[Dict[str, str]]:
        """构建RAG提示消息"""
        
        messages = []
        
        # 构建系统提示
        system_prompt = self._build_system_prompt(app, retrieved_chunks)
        messages.append({"role": "system", "content": system_prompt})
        
        # 添加对话历史（简化版，避免上下文过长）
        if conversation_history:
            # 只保留最近几轮对话
            recent_history = conversation_history[-6:]  # 最近3轮对话（6条消息）
            messages.extend(recent_history)
        
        # 添加当前查询
        messages.append({"role": "user", "content": query})
        
        return messages
    
    def _build_system_prompt(self, app: App, retrieved_chunks: List[Dict]) -> str:
        """构建系统提示"""
        
        base_prompt = app.system_prompt or "你是一个有用的AI助手。"
        
        if not retrieved_chunks:
            return base_prompt
        
        # 构建知识库内容
        knowledge_content = "\n\n".join([
            f"知识片段 {i+1}:\n{chunk['text']}"
            for i, chunk in enumerate(retrieved_chunks)
        ])
        
        rag_prompt = f"""{base_prompt}

请基于以下知识库内容来回答用户的问题。如果知识库中没有相关信息，请明确说明并基于你的通用知识来回答。

知识库内容:
{knowledge_content}

回答要求:
1. 优先使用知识库中的信息
2. 如果知识库信息不足，可以结合通用知识补充
3. 保持回答的准确性和相关性
4. 如果无法确定答案，请诚实说明
"""
        
        return rag_prompt
    
    def get_conversation_context(
        self,
        conversation_id: int,
        max_messages: int = 10,
        db: Session = None
    ) -> List[Dict[str, str]]:
        """获取对话上下文"""
        from ..models.conversation import Message, MessageType

        # 如果没有传入db会话，则创建新的
        if db is None:
            db = next(get_db())
            should_close = True
        else:
            should_close = False

        try:
            messages = db.query(Message).filter(
                Message.conversation_id == conversation_id
            ).order_by(Message.created_at.desc()).limit(max_messages).all()

            # 转换为对话格式（倒序）
            context = []
            for message in reversed(messages):
                role = "user" if message.message_type == MessageType.USER else "assistant"
                context.append({
                    "role": role,
                    "content": message.content
                })

            return context
        finally:
            if should_close:
                db.close()
    
    def extract_keywords(self, text: str) -> List[str]:
        """提取关键词（简单实现）"""
        # 这里可以使用更复杂的关键词提取算法
        import re
        
        # 移除标点符号并分词
        words = re.findall(r'\b\w+\b', text.lower())
        
        # 过滤停用词（简化版）
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
            'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had'
        }
        
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        
        # 返回前10个关键词
        return keywords[:10]
    
    async def generate_title(self, first_message: str, model: str = "gpt-3.5-turbo") -> str:
        """为对话生成标题"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": "请为以下对话生成一个简洁的标题（不超过20个字符）。只返回标题，不要其他内容。"
                },
                {
                    "role": "user",
                    "content": f"对话内容：{first_message[:200]}"
                }
            ]
            
            response = await self.llm_manager.chat_completion(
                messages=messages,
                model=model,
                temperature=0.3,
                max_tokens=50
            )
            
            if isinstance(response, dict) and 'content' in response:
                title = response['content'].strip().strip('"').strip("'")
                return title[:20]  # 限制长度
            
        except Exception as e:
            print(f"生成标题失败: {e}")
        
        # 如果生成失败，使用默认标题
        return f"对话 - {datetime.now().strftime('%m-%d %H:%M')}"


# 全局RAG引擎实例
rag_engine = RAGEngine()
